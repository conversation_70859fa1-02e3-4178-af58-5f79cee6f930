"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/langgraph.ts":
/*!******************************!*\
  !*** ./src/lib/langgraph.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingAgent: () => (/* binding */ TradingAgent),\n/* harmony export */   TradingAgentAnnotation: () => (/* binding */ TradingAgentAnnotation),\n/* harmony export */   createTradingLLM: () => (/* binding */ createTradingLLM),\n/* harmony export */   createTradingWorkflow: () => (/* binding */ createTradingWorkflow),\n/* harmony export */   tradingAgent: () => (/* binding */ tradingAgent)\n/* harmony export */ });\n/* harmony import */ var _langchain_core_messages__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @langchain/core/messages */ \"(app-pages-browser)/./node_modules/@langchain/core/messages.js\");\n/* harmony import */ var _langchain_core_tools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @langchain/core/tools */ \"(app-pages-browser)/./node_modules/@langchain/core/tools.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/v3/types.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/process/browser.js\");\n\n\n\n// 暂时注释掉 LangGraph 导入，因为版本兼容性问题\n// import { StateGraph, MessagesAnnotation, MemorySaver } from '@langchain/langgraph';\n// import { ToolNode } from '@langchain/langgraph/prebuilt';\n// import { ChatOpenAI } from '@langchain/openai';\n// 环境配置\nconst OPENAI_API_KEY = process.env.NEXT_PUBLIC_OPENAI_API_KEY;\nconst OPENAI_BASE_URL = process.env.NEXT_PUBLIC_OPENAI_BASE_URL || 'https://api.openai.com/v1';\n// 交易分析工具定义\nconst stockAnalysisTool = (0,_langchain_core_tools__WEBPACK_IMPORTED_MODULE_1__.tool)(async (param)=>{\n    let { ticker, analysisType } = param;\n    // 这里会调用后端API进行实际的股票分析\n    const response = await fetch(\"/api/analysis/stock/\".concat(ticker, \"?type=\").concat(analysisType));\n    const data = await response.json();\n    return JSON.stringify(data);\n}, {\n    name: 'stock_analysis',\n    description: '分析股票的基本面、技术面或新闻情绪',\n    schema: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        ticker: zod__WEBPACK_IMPORTED_MODULE_2__.string().describe('股票代码，如NVDA、AAPL等'),\n        analysisType: zod__WEBPACK_IMPORTED_MODULE_2__[\"enum\"]([\n            'fundamentals',\n            'technical',\n            'news',\n            'sentiment'\n        ]).describe('分析类型')\n    })\n});\nconst marketDataTool = (0,_langchain_core_tools__WEBPACK_IMPORTED_MODULE_1__.tool)(async (param)=>{\n    let { ticker, dataType, period } = param;\n    // 获取市场数据\n    const response = await fetch(\"/api/data/market/\".concat(ticker, \"?type=\").concat(dataType, \"&period=\").concat(period));\n    const data = await response.json();\n    return JSON.stringify(data);\n}, {\n    name: 'market_data',\n    description: '获取股票的市场数据，包括价格、成交量等',\n    schema: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        ticker: zod__WEBPACK_IMPORTED_MODULE_2__.string().describe('股票代码'),\n        dataType: zod__WEBPACK_IMPORTED_MODULE_2__[\"enum\"]([\n            'price',\n            'volume',\n            'indicators'\n        ]).describe('数据类型'),\n        period: zod__WEBPACK_IMPORTED_MODULE_2__[\"enum\"]([\n            '1d',\n            '1w',\n            '1m',\n            '3m',\n            '1y'\n        ]).describe('时间周期')\n    })\n});\nconst newsAnalysisTool = (0,_langchain_core_tools__WEBPACK_IMPORTED_MODULE_1__.tool)(async (param)=>{\n    let { ticker, sentiment } = param;\n    // 分析新闻情绪\n    const response = await fetch(\"/api/analysis/news/\".concat(ticker, \"?sentiment=\").concat(sentiment));\n    const data = await response.json();\n    return JSON.stringify(data);\n}, {\n    name: 'news_analysis',\n    description: '分析股票相关新闻的情绪和影响',\n    schema: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        ticker: zod__WEBPACK_IMPORTED_MODULE_2__.string().describe('股票代码'),\n        sentiment: zod__WEBPACK_IMPORTED_MODULE_2__.boolean().optional().describe('是否进行情绪分析')\n    })\n});\nconst riskAssessmentTool = (0,_langchain_core_tools__WEBPACK_IMPORTED_MODULE_1__.tool)(async (param)=>{\n    let { ticker, portfolio, riskLevel } = param;\n    // 风险评估\n    const response = await fetch('/api/analysis/risk', {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n            ticker,\n            portfolio,\n            riskLevel\n        })\n    });\n    const data = await response.json();\n    return JSON.stringify(data);\n}, {\n    name: 'risk_assessment',\n    description: '评估投资风险和组合风险',\n    schema: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        ticker: zod__WEBPACK_IMPORTED_MODULE_2__.string().describe('股票代码'),\n        portfolio: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.string()).optional().describe('投资组合中的其他股票'),\n        riskLevel: zod__WEBPACK_IMPORTED_MODULE_2__[\"enum\"]([\n            'low',\n            'medium',\n            'high'\n        ]).describe('风险偏好')\n    })\n});\n// 定义工具集合\nconst tools = [\n    stockAnalysisTool,\n    marketDataTool,\n    newsAnalysisTool,\n    riskAssessmentTool\n];\n// 模拟 LLM 模型（暂时使用简化实现）\nfunction createTradingLLM() {\n    let modelName = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'gpt-4o-mini', temperature = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n    return {\n        modelName,\n        temperature,\n        apiKey: OPENAI_API_KEY,\n        baseURL: OPENAI_BASE_URL,\n        tools,\n        async invoke (messages) {\n            // 这里应该调用实际的 OpenAI API\n            // 暂时返回模拟响应\n            return new _langchain_core_messages__WEBPACK_IMPORTED_MODULE_0__.AIMessage({\n                content: '这是一个模拟的AI响应。实际实现需要调用OpenAI API。'\n            });\n        }\n    };\n}\n// 创建交易代理状态注解\nconst TradingAgentAnnotation = {\n    ...MessagesAnnotation,\n    ticker: {\n        value: (x, y)=>y !== null && y !== void 0 ? y : x,\n        default: ()=>''\n    },\n    analysisConfig: {\n        value: (x, y)=>y !== null && y !== void 0 ? y : x,\n        default: ()=>({})\n    },\n    analysisResults: {\n        value: (x, y)=>({\n                ...x,\n                ...y\n            }),\n        default: ()=>({})\n    },\n    tradingDecision: {\n        value: (x, y)=>y !== null && y !== void 0 ? y : x,\n        default: ()=>null\n    },\n    riskAssessment: {\n        value: (x, y)=>y !== null && y !== void 0 ? y : x,\n        default: ()=>null\n    }\n};\n// 判断是否继续执行的函数\nfunction shouldContinue(state) {\n    var _lastMessage_tool_calls;\n    const lastMessage = state.messages[state.messages.length - 1];\n    // 如果LLM调用了工具，则路由到工具节点\n    if ((_lastMessage_tool_calls = lastMessage.tool_calls) === null || _lastMessage_tool_calls === void 0 ? void 0 : _lastMessage_tool_calls.length) {\n        return 'tools';\n    }\n    // 否则结束执行\n    return '__end__';\n}\n// 调用模型的函数\nasync function callModel(state) {\n    const model = createTradingLLM();\n    // 构建系统提示\n    const systemPrompt = \"你是一个专业的金融交易分析师。你的任务是：\\n1. 分析股票的基本面、技术面和新闻情绪\\n2. 评估投资风险\\n3. 提供明确的交易建议\\n\\n当前分析的股票代码是: \".concat(state.ticker, \"\\n\\n请使用可用的工具来收集和分析数据，然后提供专业的交易建议。\");\n    const messages = [\n        new _langchain_core_messages__WEBPACK_IMPORTED_MODULE_0__.HumanMessage(systemPrompt),\n        ...state.messages\n    ];\n    const response = await model.invoke(messages);\n    return {\n        messages: [\n            response\n        ]\n    };\n}\n// 分析节点函数\nasync function analyzeStock(state) {\n    const model = createTradingLLM();\n    const analysisPrompt = \"请对股票 \".concat(state.ticker, \" 进行全面分析，包括：\\n1. 基本面分析\\n2. 技术面分析  \\n3. 新闻情绪分析\\n4. 风险评估\\n\\n请使用相应的工具来获取数据并进行分析。\");\n    const response = await model.invoke([\n        new _langchain_core_messages__WEBPACK_IMPORTED_MODULE_0__.HumanMessage(analysisPrompt)\n    ]);\n    return {\n        messages: [\n            response\n        ],\n        analysisResults: {\n            completed: true,\n            timestamp: new Date().toISOString()\n        }\n    };\n}\n// 生成交易决策的函数\nasync function generateTradingDecision(state) {\n    const model = createTradingLLM();\n    const decisionPrompt = \"基于之前的分析结果，请为股票 \".concat(state.ticker, \" 生成具体的交易决策，包括：\\n1. 交易行动（买入/卖出/持有）\\n2. 目标价格\\n3. 止损价格\\n4. 仓位大小建议\\n5. 风险等级\\n6. 决策理由\\n\\n请提供明确、可执行的交易建议。\");\n    const response = await model.invoke([\n        ...state.messages,\n        new _langchain_core_messages__WEBPACK_IMPORTED_MODULE_0__.HumanMessage(decisionPrompt)\n    ]);\n    // 这里可以解析响应并提取结构化的交易决策\n    const tradingDecision = {\n        action: 'hold',\n        confidence: 75,\n        reasoning: response.content,\n        timestamp: new Date().toISOString()\n    };\n    return {\n        messages: [\n            response\n        ],\n        tradingDecision\n    };\n}\n// 创建交易分析工作流\nfunction createTradingWorkflow() {\n    const workflow = new StateGraph(TradingAgentAnnotation)// 添加节点\n    .addNode('agent', callModel).addNode('tools', toolNode).addNode('analyze', analyzeStock).addNode('decide', generateTradingDecision)// 添加边\n    .addEdge('__start__', 'agent').addEdge('tools', 'agent').addConditionalEdges('agent', shouldContinue).addEdge('analyze', 'decide').addEdge('decide', '__end__');\n    // 添加内存保存器\n    const checkpointer = new MemorySaver();\n    return workflow.compile({\n        checkpointer\n    });\n}\n// 简化的交易代理类\nclass TradingAgent {\n    async analyze(ticker) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const initialState = {\n            messages: [\n                new _langchain_core_messages__WEBPACK_IMPORTED_MODULE_0__.HumanMessage(\"请分析股票 \".concat(ticker))\n            ],\n            ticker,\n            analysisConfig: config\n        };\n        const result = await this.workflow.invoke(initialState, {\n            configurable: {\n                thread_id: \"analysis_\".concat(ticker, \"_\").concat(Date.now())\n            }\n        });\n        return result;\n    }\n    async chat(message, threadId) {\n        const result = await this.workflow.invoke({\n            messages: [\n                new _langchain_core_messages__WEBPACK_IMPORTED_MODULE_0__.HumanMessage(message)\n            ]\n        }, {\n            configurable: {\n                thread_id: threadId\n            }\n        });\n        return result;\n    }\n    async getState(threadId) {\n        return await this.workflow.getState({\n            configurable: {\n                thread_id: threadId\n            }\n        });\n    }\n    async streamAnalysis(ticker) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const initialState = {\n            messages: [\n                new _langchain_core_messages__WEBPACK_IMPORTED_MODULE_0__.HumanMessage(\"请分析股票 \".concat(ticker))\n            ],\n            ticker,\n            analysisConfig: config\n        };\n        return this.workflow.stream(initialState, {\n            configurable: {\n                thread_id: \"stream_\".concat(ticker, \"_\").concat(Date.now())\n            }\n        });\n    }\n    constructor(){\n        this.workflow = createTradingWorkflow();\n    }\n}\n// 导出默认实例\nconst tradingAgent = new TradingAgent();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/langgraph.ts\n"));

/***/ })

});