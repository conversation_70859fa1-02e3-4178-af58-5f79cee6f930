# LangGraph 集成指南

## 📋 概述

TradingAgents Frontend 现已集成 LangGraph.js，提供强大的智能工作流和对话式分析功能。LangGraph 是一个用于构建有状态、多参与者应用程序的库，特别适合复杂的金融分析场景。

## 🌟 主要特性

### 1. 智能工作流
- **状态管理**: 自动管理分析过程中的状态转换
- **并行执行**: 支持多个分析任务并行处理
- **错误恢复**: 自动重试和错误处理机制
- **检查点**: 支持工作流的暂停和恢复

### 2. 对话式分析
- **自然语言交互**: 通过对话进行股票分析
- **上下文记忆**: 保持对话历史和分析上下文
- **智能工具调用**: 自动选择和调用合适的分析工具

### 3. 可视化工作流
- **实时监控**: 可视化显示工作流执行状态
- **节点状态**: 实时更新每个分析节点的状态
- **执行路径**: 显示分析的执行路径和决策点

## 🚀 快速开始

### 1. 启用 LangGraph

在分析配置页面中：

1. 勾选 "启用 LangGraph 智能工作流"
2. 配置 LangGraph 选项：
   - 启用工作流可视化
   - 启用对话记忆
   - 启用智能工具调用

### 2. 使用对话式分析

```typescript
// 在 LangGraph 聊天界面中
"请分析 NVDA 股票的投资价值"
"这只股票的风险如何？"
"给我一些具体的投资建议"
```

### 3. 监控工作流

在工作流标签页中可以看到：
- 当前执行的节点
- 每个节点的状态和耗时
- 节点间的依赖关系
- 执行结果和输出

## 🔧 技术架构

### 核心组件

```
LangGraph 集成
├── lib/langgraph.ts          # 核心 LangGraph 配置
├── hooks/useLangGraphAgent.ts # React Hook
├── components/langgraph/     # UI 组件
│   ├── LangGraphChat.tsx     # 对话界面
│   ├── WorkflowVisualization.tsx # 工作流可视化
│   └── LangGraphConfig.tsx   # 配置界面
```

### 工作流定义

```typescript
// 默认交易分析工作流
const tradingWorkflow = {
  nodes: [
    'start',           // 开始
    'data_collection', // 数据收集
    'fundamental_analysis', // 基本面分析
    'technical_analysis',   // 技术分析
    'sentiment_analysis',   // 情绪分析
    'risk_assessment',      // 风险评估
    'decision_making',      // 决策制定
    'end'              // 完成
  ],
  edges: [
    // 定义节点间的连接关系
  ]
};
```

### 工具集成

LangGraph 集成了以下分析工具：

1. **股票分析工具** (`stockAnalysisTool`)
   - 基本面分析
   - 技术面分析
   - 新闻情绪分析

2. **市场数据工具** (`marketDataTool`)
   - 价格数据获取
   - 成交量分析
   - 技术指标计算

3. **新闻分析工具** (`newsAnalysisTool`)
   - 新闻情绪分析
   - 影响评估

4. **风险评估工具** (`riskAssessmentTool`)
   - 投资风险评估
   - 组合风险分析

## 📊 使用示例

### 1. 基础股票分析

```typescript
import { useLangGraphAgent } from '@/hooks/useLangGraphAgent';

function MyComponent() {
  const { analyzeStock, messages, isProcessing } = useLangGraphAgent();
  
  const handleAnalyze = async () => {
    await analyzeStock('NVDA', {
      analysisType: 'comprehensive',
      includeRisk: true,
      includeSentiment: true,
    });
  };
  
  return (
    <div>
      <button onClick={handleAnalyze} disabled={isProcessing}>
        分析股票
      </button>
      {/* 显示分析结果 */}
    </div>
  );
}
```

### 2. 对话式交互

```typescript
const { sendMessage, messages } = useLangGraphAgent();

// 发送消息
await sendMessage("请分析 AAPL 的投资价值");

// 查看回复
console.log(messages);
```

### 3. 流式分析

```typescript
const { streamAnalysis } = useLangGraphAgent();

// 流式分析，实时获取结果
for await (const chunk of streamAnalysis('TSLA')) {
  console.log('分析进度:', chunk);
}
```

## ⚙️ 配置选项

### LLM 配置

```typescript
const llmConfig = {
  provider: 'openai',      // 模型提供商
  model: 'gpt-4o-mini',    // 模型名称
  temperature: 0.1,        // 温度参数
  maxTokens: 4000,         // 最大令牌数
};
```

### 工作流配置

```typescript
const workflowConfig = {
  enableParallelExecution: true,  // 并行执行
  maxRetries: 3,                  // 最大重试次数
  timeout: 300000,                // 超时时间
  checkpointInterval: 10000,      // 检查点间隔
};
```

### 内存配置

```typescript
const memoryConfig = {
  enabled: true,           // 启用内存
  maxMessages: 100,        // 最大消息数
  persistToStorage: true,  // 持久化存储
};
```

## 🔍 调试和监控

### 启用调试模式

```typescript
const debugConfig = {
  enabled: true,           // 启用调试
  logLevel: 'info',        // 日志级别
  traceExecution: true,    // 跟踪执行
};
```

### 查看执行状态

```typescript
const { getAgentState } = useLangGraphAgent();

// 获取当前状态
const state = await getAgentState();
console.log('当前状态:', state);
```

### 监控工作流

在工作流可视化组件中可以看到：
- 节点执行状态
- 执行时间
- 输出结果
- 错误信息

## 🎯 最佳实践

### 1. 工具使用

- 合理配置工具权限
- 避免不必要的工具调用
- 处理工具调用错误

### 2. 内存管理

- 定期清理对话历史
- 控制内存使用量
- 合理设置持久化策略

### 3. 性能优化

- 启用并行执行
- 设置合理的超时时间
- 使用检查点机制

### 4. 错误处理

- 配置重试策略
- 监控执行状态
- 提供用户友好的错误信息

## 🔧 自定义扩展

### 添加自定义工具

```typescript
import { tool } from '@langchain/core/tools';
import { z } from 'zod';

const customTool = tool(
  async ({ param1, param2 }) => {
    // 自定义工具逻辑
    return 'result';
  },
  {
    name: 'custom_tool',
    description: '自定义工具描述',
    schema: z.object({
      param1: z.string(),
      param2: z.number(),
    }),
  }
);
```

### 自定义工作流节点

```typescript
async function customNode(state: AgentState) {
  // 自定义节点逻辑
  return {
    messages: [...state.messages, newMessage],
    customData: processedData,
  };
}
```

## 📚 相关资源

- [LangGraph.js 官方文档](https://langchain-ai.github.io/langgraphjs/)
- [LangChain.js 文档](https://js.langchain.com/)
- [OpenAI API 文档](https://platform.openai.com/docs)

## 🐛 故障排除

### 常见问题

1. **工具调用失败**
   - 检查 API 密钥配置
   - 确认网络连接
   - 查看错误日志

2. **内存不足**
   - 减少最大消息数
   - 清理对话历史
   - 禁用持久化存储

3. **执行超时**
   - 增加超时时间
   - 检查网络延迟
   - 优化工具调用

### 调试技巧

- 启用调试模式查看详细日志
- 使用工作流可视化监控执行
- 检查浏览器开发者工具的网络面板

---

通过 LangGraph 集成，TradingAgents 现在具备了更强大的智能分析能力和更灵活的交互方式。您可以通过自然语言与系统对话，获得更深入的分析洞察。
