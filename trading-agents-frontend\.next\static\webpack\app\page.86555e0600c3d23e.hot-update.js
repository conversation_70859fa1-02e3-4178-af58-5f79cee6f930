"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/TradingDashboard.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/TradingDashboard.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingDashboard: () => (/* binding */ TradingDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _AgentStatusPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AgentStatusPanel */ \"(app-pages-browser)/./src/components/dashboard/AgentStatusPanel.tsx\");\n/* harmony import */ var _AnalysisProgress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AnalysisProgress */ \"(app-pages-browser)/./src/components/dashboard/AnalysisProgress.tsx\");\n/* harmony import */ var _RealtimeDataPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./RealtimeDataPanel */ \"(app-pages-browser)/./src/components/dashboard/RealtimeDataPanel.tsx\");\n/* harmony import */ var _ReportViewer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ReportViewer */ \"(app-pages-browser)/./src/components/dashboard/ReportViewer.tsx\");\n/* harmony import */ var _TradingDecision__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TradingDecision */ \"(app-pages-browser)/./src/components/dashboard/TradingDecision.tsx\");\n/* harmony import */ var _hooks_useTradingAnalysis__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useTradingAnalysis */ \"(app-pages-browser)/./src/hooks/useTradingAnalysis.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingDashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction TradingDashboard(param) {\n    let { config, onBack } = param;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const { analysisState, agentStatuses, reports, finalDecision, startAnalysis, isAnalyzing } = (0,_hooks_useTradingAnalysis__WEBPACK_IMPORTED_MODULE_8__.useTradingAnalysis)(config);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingDashboard.useEffect\": ()=>{\n            // 自动开始分析\n            startAnalysis();\n        }\n    }[\"TradingDashboard.useEffect\"], [\n        startAnalysis\n    ]);\n    const tabs = [\n        {\n            id: 'overview',\n            name: '总览',\n            icon: '📊'\n        },\n        {\n            id: 'agents',\n            name: '代理状态',\n            icon: '🤖'\n        },\n        {\n            id: 'data',\n            name: '实时数据',\n            icon: '📈'\n        },\n        {\n            id: 'reports',\n            name: '分析报告',\n            icon: '📋'\n        },\n        {\n            id: 'decision',\n            name: '交易决策',\n            icon: '💼'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-slate-900 dark:text-white\",\n                                children: [\n                                    config.ticker,\n                                    \" 交易分析\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-600 dark:text-slate-400\",\n                                children: [\n                                    \"分析日期: \",\n                                    config.analysisDate\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: isAnalyzing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-blue-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"分析进行中...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AnalysisProgress__WEBPACK_IMPORTED_MODULE_4__.AnalysisProgress, {\n                currentStage: analysisState.currentStage,\n                progress: analysisState.progress,\n                isComplete: analysisState.isComplete\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-slate-200 dark:border-slate-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeTab === tab.id ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-2\",\n                                    children: tab.icon\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this),\n                                tab.name\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    x: 20\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: [\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            children: \"分析概览\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600 dark:text-slate-400\",\n                                                            children: \"股票标识:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: config.ticker\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600 dark:text-slate-400\",\n                                                            children: \"分析日期:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: config.analysisDate\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600 dark:text-slate-400\",\n                                                            children: \"选择的分析师:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: [\n                                                                config.selectedAnalysts.length,\n                                                                \"个\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600 dark:text-slate-400\",\n                                                            children: \"研究深度:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: config.researchDepth === 'quick' ? '快速' : config.researchDepth === 'standard' ? '标准' : '深度'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600 dark:text-slate-400\",\n                                                            children: \"在线工具:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold \".concat(config.onlineTools ? 'text-green-600' : 'text-red-600'),\n                                                            children: config.onlineTools ? '启用' : '禁用'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            children: \"当前状态\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                                            children: [\n                                                                analysisState.progress,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-slate-600 dark:text-slate-400\",\n                                                            children: \"分析进度\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-slate-700 dark:text-slate-300\",\n                                                            children: [\n                                                                \"当前阶段: \",\n                                                                analysisState.currentStage\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-600 h-2 rounded-full transition-all duration-500\",\n                                                                style: {\n                                                                    width: \"\".concat(analysisState.progress, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this),\n                                                analysisState.isComplete && finalDecision && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-green-800 dark:text-green-200\",\n                                                            children: \"分析完成！\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-green-600 dark:text-green-400 mt-1\",\n                                                            children: '交易决策已生成，请查看\"交易决策\"标签页'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'agents' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AgentStatusPanel__WEBPACK_IMPORTED_MODULE_3__.AgentStatusPanel, {\n                        agentStatuses: agentStatuses,\n                        selectedAnalysts: config.selectedAnalysts\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'data' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RealtimeDataPanel__WEBPACK_IMPORTED_MODULE_5__.RealtimeDataPanel, {\n                        ticker: config.ticker,\n                        analysisDate: config.analysisDate\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'reports' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReportViewer__WEBPACK_IMPORTED_MODULE_6__.ReportViewer, {\n                        reports: reports,\n                        selectedAnalysts: config.selectedAnalysts\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'decision' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TradingDecision__WEBPACK_IMPORTED_MODULE_7__.TradingDecision, {\n                        decision: finalDecision,\n                        isComplete: analysisState.isComplete,\n                        ticker: config.ticker\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, activeTab, true, {\n                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(TradingDashboard, \"bH/Um3m5YsaIzF7ZfDkG7stHLPs=\", false, function() {\n    return [\n        _hooks_useTradingAnalysis__WEBPACK_IMPORTED_MODULE_8__.useTradingAnalysis\n    ];\n});\n_c = TradingDashboard;\nvar _c;\n$RefreshReg$(_c, \"TradingDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/TradingDashboard.tsx\n"));

/***/ })

});