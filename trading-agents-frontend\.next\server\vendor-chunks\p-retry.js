"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/p-retry";
exports.ids = ["vendor-chunks/p-retry"];
exports.modules = {

/***/ "(ssr)/./node_modules/p-retry/index.js":
/*!***************************************!*\
  !*** ./node_modules/p-retry/index.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst retry = __webpack_require__(/*! retry */ \"(ssr)/./node_modules/retry/index.js\");\n\nconst networkErrorMsgs = [\n\t'Failed to fetch', // Chrome\n\t'NetworkError when attempting to fetch resource.', // Firefox\n\t'The Internet connection appears to be offline.', // Safari\n\t'Network request failed' // `cross-fetch`\n];\n\nclass AbortError extends Error {\n\tconstructor(message) {\n\t\tsuper();\n\n\t\tif (message instanceof Error) {\n\t\t\tthis.originalError = message;\n\t\t\t({message} = message);\n\t\t} else {\n\t\t\tthis.originalError = new Error(message);\n\t\t\tthis.originalError.stack = this.stack;\n\t\t}\n\n\t\tthis.name = 'AbortError';\n\t\tthis.message = message;\n\t}\n}\n\nconst decorateErrorWithCounts = (error, attemptNumber, options) => {\n\t// Minus 1 from attemptNumber because the first attempt does not count as a retry\n\tconst retriesLeft = options.retries - (attemptNumber - 1);\n\n\terror.attemptNumber = attemptNumber;\n\terror.retriesLeft = retriesLeft;\n\treturn error;\n};\n\nconst isNetworkError = errorMessage => networkErrorMsgs.includes(errorMessage);\n\nconst pRetry = (input, options) => new Promise((resolve, reject) => {\n\toptions = {\n\t\tonFailedAttempt: () => {},\n\t\tretries: 10,\n\t\t...options\n\t};\n\n\tconst operation = retry.operation(options);\n\n\toperation.attempt(async attemptNumber => {\n\t\ttry {\n\t\t\tresolve(await input(attemptNumber));\n\t\t} catch (error) {\n\t\t\tif (!(error instanceof Error)) {\n\t\t\t\treject(new TypeError(`Non-error was thrown: \"${error}\". You should only throw errors.`));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (error instanceof AbortError) {\n\t\t\t\toperation.stop();\n\t\t\t\treject(error.originalError);\n\t\t\t} else if (error instanceof TypeError && !isNetworkError(error.message)) {\n\t\t\t\toperation.stop();\n\t\t\t\treject(error);\n\t\t\t} else {\n\t\t\t\tdecorateErrorWithCounts(error, attemptNumber, options);\n\n\t\t\t\ttry {\n\t\t\t\t\tawait options.onFailedAttempt(error);\n\t\t\t\t} catch (error) {\n\t\t\t\t\treject(error);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (!operation.retry(error)) {\n\t\t\t\t\treject(operation.mainError());\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t});\n});\n\nmodule.exports = pRetry;\n// TODO: remove this in the next major version\nmodule.exports[\"default\"] = pRetry;\n\nmodule.exports.AbortError = AbortError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/p-retry/index.js\n");

/***/ })

};
;