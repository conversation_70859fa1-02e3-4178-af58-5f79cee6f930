{"name": "trading-agents-frontend", "version": "1.0.0", "description": "TradingAgents 多智能体大语言模型金融交易框架 - 前端界面", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tanstack/react-query": "^5.8.4", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.4.16", "axios": "^1.6.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "lucide-react": "^0.292.0", "next": "^15.3.5", "postcss": "^8.4.31", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "zustand": "^4.4.6", "@langchain/core": "^0.3.6", "@langchain/langgraph": "^0.3.6", "@langchain/openai": "^0.3.6", "@langchain/community": "^0.3.6", "zod": "^3.22.4"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "eslint": "^8.0.0", "eslint-config-next": "^15.3.5"}, "engines": {"node": ">=18.0.0"}}