[{"D:\\project\\TradingAgents\\trading-agents-frontend\\src\\app\\api\\health\\route.ts": "1", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\app\\layout.tsx": "2", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\app\\page.tsx": "3", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\common\\ConnectionStatus.tsx": "4", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\common\\ErrorBoundary.tsx": "5", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\dashboard\\AgentStatusPanel.tsx": "6", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\dashboard\\AnalysisProgress.tsx": "7", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\dashboard\\RealtimeDataPanel.tsx": "8", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\dashboard\\ReportViewer.tsx": "9", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\dashboard\\TradingDashboard.tsx": "10", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\dashboard\\TradingDecision.tsx": "11", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\langgraph\\LangGraphChat.tsx": "12", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\langgraph\\LangGraphConfig.tsx": "13", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\langgraph\\WorkflowVisualization.tsx": "14", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\layout\\Footer.tsx": "15", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\layout\\Header.tsx": "16", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\providers.tsx": "17", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\ui\\Badge.tsx": "18", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\ui\\Button.tsx": "19", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\ui\\Card.tsx": "20", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\ui\\LoadingSpinner.tsx": "21", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\ui\\Modal.tsx": "22", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\ui\\Tooltip.tsx": "23", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\welcome\\AnalysisConfigForm.tsx": "24", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\welcome\\WelcomeScreen.tsx": "25", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\hooks\\useLangGraphAgent.ts": "26", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\hooks\\useLocalStorage.ts": "27", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\hooks\\useTradingAnalysis.ts": "28", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\hooks\\useWebSocket.ts": "29", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\lib\\api.ts": "30", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\lib\\langgraph.ts": "31", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\store\\analysisStore.ts": "32", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\types\\index.ts": "33", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\utils\\constants.ts": "34", "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\utils\\helpers.ts": "35"}, {"size": 1158, "mtime": 1751639677742, "results": "36", "hashOfConfig": "37"}, {"size": 1886, "mtime": 1751639879541, "results": "38", "hashOfConfig": "37"}, {"size": 1567, "mtime": 1751637442101, "results": "39", "hashOfConfig": "37"}, {"size": 5715, "mtime": 1751639856653, "results": "40", "hashOfConfig": "37"}, {"size": 4955, "mtime": 1751639766748, "results": "41", "hashOfConfig": "37"}, {"size": 12417, "mtime": 1751637761275, "results": "42", "hashOfConfig": "37"}, {"size": 7052, "mtime": 1751637650342, "results": "43", "hashOfConfig": "37"}, {"size": 12687, "mtime": 1751637817616, "results": "44", "hashOfConfig": "37"}, {"size": 8159, "mtime": 1751637849900, "results": "45", "hashOfConfig": "37"}, {"size": 8979, "mtime": 1751640338774, "results": "46", "hashOfConfig": "37"}, {"size": 10755, "mtime": 1751637890684, "results": "47", "hashOfConfig": "37"}, {"size": 9871, "mtime": 1751640260440, "results": "48", "hashOfConfig": "37"}, {"size": 13822, "mtime": 1751640389702, "results": "49", "hashOfConfig": "37"}, {"size": 10656, "mtime": 1751640303435, "results": "50", "hashOfConfig": "37"}, {"size": 2405, "mtime": 1751637481327, "results": "51", "hashOfConfig": "37"}, {"size": 2353, "mtime": 1751637467387, "results": "52", "hashOfConfig": "37"}, {"size": 547, "mtime": 1751637454442, "results": "53", "hashOfConfig": "37"}, {"size": 2856, "mtime": 1751639728647, "results": "54", "hashOfConfig": "37"}, {"size": 2397, "mtime": 1751637494796, "results": "55", "hashOfConfig": "37"}, {"size": 1648, "mtime": 1751637505687, "results": "56", "hashOfConfig": "37"}, {"size": 1797, "mtime": 1751639690314, "results": "57", "hashOfConfig": "37"}, {"size": 4560, "mtime": 1751639710400, "results": "58", "hashOfConfig": "37"}, {"size": 2683, "mtime": 1751639743921, "results": "59", "hashOfConfig": "37"}, {"size": 14238, "mtime": 1751640422880, "results": "60", "hashOfConfig": "37"}, {"size": 6617, "mtime": 1751637545221, "results": "61", "hashOfConfig": "37"}, {"size": 8470, "mtime": 1751640219942, "results": "62", "hashOfConfig": "37"}, {"size": 2221, "mtime": 1751639780144, "results": "63", "hashOfConfig": "37"}, {"size": 6575, "mtime": 1751637718073, "results": "64", "hashOfConfig": "37"}, {"size": 4831, "mtime": 1751639802492, "results": "65", "hashOfConfig": "37"}, {"size": 6395, "mtime": 1751637687301, "results": "66", "hashOfConfig": "37"}, {"size": 8693, "mtime": 1751640183569, "results": "67", "hashOfConfig": "37"}, {"size": 6757, "mtime": 1751639831097, "results": "68", "hashOfConfig": "37"}, {"size": 6115, "mtime": 1751637970556, "results": "69", "hashOfConfig": "37"}, {"size": 7345, "mtime": 1751638011612, "results": "70", "hashOfConfig": "37"}, {"size": 8530, "mtime": 1751638049512, "results": "71", "hashOfConfig": "37"}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1dezes0", {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\app\\api\\health\\route.ts", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\app\\layout.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\app\\page.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\common\\ConnectionStatus.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\common\\ErrorBoundary.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\dashboard\\AgentStatusPanel.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\dashboard\\AnalysisProgress.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\dashboard\\RealtimeDataPanel.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\dashboard\\ReportViewer.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\dashboard\\TradingDashboard.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\dashboard\\TradingDecision.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\langgraph\\LangGraphChat.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\langgraph\\LangGraphConfig.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\langgraph\\WorkflowVisualization.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\layout\\Footer.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\layout\\Header.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\providers.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\ui\\Badge.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\ui\\Button.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\ui\\Card.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\ui\\LoadingSpinner.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\ui\\Modal.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\ui\\Tooltip.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\welcome\\AnalysisConfigForm.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\components\\welcome\\WelcomeScreen.tsx", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\hooks\\useLangGraphAgent.ts", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\hooks\\useLocalStorage.ts", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\hooks\\useTradingAnalysis.ts", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\hooks\\useWebSocket.ts", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\lib\\api.ts", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\lib\\langgraph.ts", ["177"], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\store\\analysisStore.ts", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\types\\index.ts", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\utils\\constants.ts", [], [], "D:\\project\\TradingAgents\\trading-agents-frontend\\src\\utils\\helpers.ts", [], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "178", "line": 106, "column": 43, "nodeType": null}, "Parsing error: Expression expected."]