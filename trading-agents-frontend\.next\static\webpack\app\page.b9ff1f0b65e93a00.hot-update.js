"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/TradingDashboard.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/TradingDashboard.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingDashboard: () => (/* binding */ TradingDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _AgentStatusPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AgentStatusPanel */ \"(app-pages-browser)/./src/components/dashboard/AgentStatusPanel.tsx\");\n/* harmony import */ var _AnalysisProgress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AnalysisProgress */ \"(app-pages-browser)/./src/components/dashboard/AnalysisProgress.tsx\");\n/* harmony import */ var _RealtimeDataPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./RealtimeDataPanel */ \"(app-pages-browser)/./src/components/dashboard/RealtimeDataPanel.tsx\");\n/* harmony import */ var _ReportViewer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ReportViewer */ \"(app-pages-browser)/./src/components/dashboard/ReportViewer.tsx\");\n/* harmony import */ var _TradingDecision__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TradingDecision */ \"(app-pages-browser)/./src/components/dashboard/TradingDecision.tsx\");\n/* harmony import */ var _hooks_useTradingAnalysis__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useTradingAnalysis */ \"(app-pages-browser)/./src/hooks/useTradingAnalysis.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingDashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction TradingDashboard(param) {\n    let { config, onBack } = param;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const { analysisState, agentStatuses, reports, finalDecision, startAnalysis, isAnalyzing } = (0,_hooks_useTradingAnalysis__WEBPACK_IMPORTED_MODULE_8__.useTradingAnalysis)(config);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingDashboard.useEffect\": ()=>{\n            // 自动开始分析\n            startAnalysis();\n        }\n    }[\"TradingDashboard.useEffect\"], [\n        startAnalysis\n    ]);\n    const tabs = [\n        {\n            id: 'overview',\n            name: '总览',\n            icon: '📊'\n        },\n        {\n            id: 'agents',\n            name: '代理状态',\n            icon: '🤖'\n        },\n        {\n            id: 'data',\n            name: '实时数据',\n            icon: '📈'\n        },\n        {\n            id: 'reports',\n            name: '分析报告',\n            icon: '📋'\n        },\n        {\n            id: 'decision',\n            name: '交易决策',\n            icon: '💼'\n        },\n        {\n            id: 'langgraph',\n            name: 'LangGraph',\n            icon: '🧠'\n        },\n        {\n            id: 'workflow',\n            name: '工作流',\n            icon: '🔄'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-slate-900 dark:text-white\",\n                                children: [\n                                    config.ticker,\n                                    \" 交易分析\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-600 dark:text-slate-400\",\n                                children: [\n                                    \"分析日期: \",\n                                    config.analysisDate\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: isAnalyzing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-blue-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"分析进行中...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AnalysisProgress__WEBPACK_IMPORTED_MODULE_4__.AnalysisProgress, {\n                currentStage: analysisState.currentStage,\n                progress: analysisState.progress,\n                isComplete: analysisState.isComplete\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-slate-200 dark:border-slate-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeTab === tab.id ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-2\",\n                                    children: tab.icon\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this),\n                                tab.name\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    x: 20\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: [\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            children: \"分析概览\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600 dark:text-slate-400\",\n                                                            children: \"股票标识:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: config.ticker\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600 dark:text-slate-400\",\n                                                            children: \"分析日期:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: config.analysisDate\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600 dark:text-slate-400\",\n                                                            children: \"选择的分析师:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: [\n                                                                config.selectedAnalysts.length,\n                                                                \"个\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600 dark:text-slate-400\",\n                                                            children: \"研究深度:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: config.researchDepth === 'quick' ? '快速' : config.researchDepth === 'standard' ? '标准' : '深度'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-600 dark:text-slate-400\",\n                                                            children: \"在线工具:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold \".concat(config.onlineTools ? 'text-green-600' : 'text-red-600'),\n                                                            children: config.onlineTools ? '启用' : '禁用'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            children: \"当前状态\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                                            children: [\n                                                                analysisState.progress,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-slate-600 dark:text-slate-400\",\n                                                            children: \"分析进度\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-slate-700 dark:text-slate-300\",\n                                                            children: [\n                                                                \"当前阶段: \",\n                                                                analysisState.currentStage\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-blue-600 h-2 rounded-full transition-all duration-500\",\n                                                                style: {\n                                                                    width: \"\".concat(analysisState.progress, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this),\n                                                analysisState.isComplete && finalDecision && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-green-800 dark:text-green-200\",\n                                                            children: \"分析完成！\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-green-600 dark:text-green-400 mt-1\",\n                                                            children: '交易决策已生成，请查看\"交易决策\"标签页'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'agents' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AgentStatusPanel__WEBPACK_IMPORTED_MODULE_3__.AgentStatusPanel, {\n                        agentStatuses: agentStatuses,\n                        selectedAnalysts: config.selectedAnalysts\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'data' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RealtimeDataPanel__WEBPACK_IMPORTED_MODULE_5__.RealtimeDataPanel, {\n                        ticker: config.ticker,\n                        analysisDate: config.analysisDate\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'reports' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReportViewer__WEBPACK_IMPORTED_MODULE_6__.ReportViewer, {\n                        reports: reports,\n                        selectedAnalysts: config.selectedAnalysts\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'decision' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TradingDecision__WEBPACK_IMPORTED_MODULE_7__.TradingDecision, {\n                        decision: finalDecision,\n                        isComplete: analysisState.isComplete,\n                        ticker: config.ticker\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, activeTab, true, {\n                fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project\\\\TradingAgents\\\\trading-agents-frontend\\\\src\\\\components\\\\dashboard\\\\TradingDashboard.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(TradingDashboard, \"bH/Um3m5YsaIzF7ZfDkG7stHLPs=\", false, function() {\n    return [\n        _hooks_useTradingAnalysis__WEBPACK_IMPORTED_MODULE_8__.useTradingAnalysis\n    ];\n});\n_c = TradingDashboard;\nvar _c;\n$RefreshReg$(_c, \"TradingDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/TradingDashboard.tsx\n"));

/***/ })

});