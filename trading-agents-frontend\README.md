# TradingAgents Frontend

<p align="center">
  <img src="https://img.shields.io/badge/Next.js-14.0-black?logo=next.js" alt="Next.js" />
  <img src="https://img.shields.io/badge/React-18.0-blue?logo=react" alt="React" />
  <img src="https://img.shields.io/badge/TypeScript-5.0-blue?logo=typescript" alt="TypeScript" />
  <img src="https://img.shields.io/badge/Tailwind-3.3-blue?logo=tailwindcss" alt="Tailwind CSS" />
</p>

TradingAgents 多智能体大语言模型金融交易框架的前端界面，基于 Next.js 构建的现代化 Web 应用程序。

## 🌟 特性

- **🎨 现代化界面**: 基于 Tailwind CSS 的响应式设计
- **🚀 高性能**: Next.js 14 + React 18 的最新技术栈
- **🔄 实时更新**: WebSocket 实时数据推送
- **📊 数据可视化**: 丰富的图表和数据展示
- **🌐 中文本地化**: 完整的中文界面支持
- **📱 移动端适配**: 完美的移动端体验
- **🎭 动画效果**: Framer Motion 流畅动画
- **🔧 TypeScript**: 完整的类型安全
- **🧠 LangGraph 集成**: 智能工作流和对话式分析
- **🤖 AI 驱动**: 基于大语言模型的智能分析

## 🏗️ 技术栈

### 核心框架
- **Next.js 14**: React 全栈框架
- **React 18**: 用户界面库
- **TypeScript**: 类型安全的 JavaScript

### 样式和UI
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Headless UI**: 无样式的可访问组件
- **Heroicons**: 精美的 SVG 图标
- **Lucide React**: 现代图标库
- **Framer Motion**: 动画库

### 数据管理
- **TanStack Query**: 服务器状态管理
- **Zustand**: 客户端状态管理
- **Axios**: HTTP 客户端

### AI 和工作流
- **LangGraph.js**: 智能工作流引擎
- **LangChain.js**: 大语言模型集成
- **Zod**: 数据验证和类型安全

### 数据可视化
- **Recharts**: React 图表库

### 开发工具
- **ESLint**: 代码检查
- **PostCSS**: CSS 处理
- **Autoprefixer**: CSS 前缀自动添加

## 🚀 快速开始

### 环境要求

- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd trading-agents-frontend
```

2. **安装依赖**
```bash
npm install
# 或
yarn install
```

3. **环境配置**
```bash
cp .env.local.example .env.local
```

编辑 `.env.local` 文件，配置必要的环境变量：

```env
# API配置
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# OpenAI API配置
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key
NEXT_PUBLIC_OPENAI_BASE_URL=https://api.nuwaapi.com

# FinnHub API配置
NEXT_PUBLIC_FINNHUB_API_KEY=your_finnhub_api_key
```

4. **启动开发服务器**
```bash
npm run dev
# 或
yarn dev
```

5. **访问应用**

打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 📁 项目结构

```
trading-agents-frontend/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局
│   │   └── page.tsx           # 首页
│   ├── components/            # React 组件
│   │   ├── dashboard/         # 仪表板组件
│   │   ├── langgraph/         # LangGraph 组件
│   │   ├── layout/           # 布局组件
│   │   ├── ui/               # 基础 UI 组件
│   │   ├── common/           # 通用组件
│   │   └── welcome/          # 欢迎页组件
│   ├── hooks/                # 自定义 Hooks
│   ├── lib/                  # 工具库
│   │   ├── api.ts           # API 接口
│   │   └── langgraph.ts     # LangGraph 配置
│   ├── store/                # 状态管理
│   ├── types/                # TypeScript 类型定义
│   └── utils/                # 工具函数
├── public/                  # 静态资源
├── .env.local.example      # 环境变量示例
├── next.config.js          # Next.js 配置
├── tailwind.config.js      # Tailwind CSS 配置
├── tsconfig.json          # TypeScript 配置
└── package.json           # 项目依赖
```

## 🎯 主要功能

### 1. 欢迎页面
- 项目介绍和功能展示
- 分析配置表单
- 工作流程说明

### 2. 交易仪表板
- **总览**: 分析进度和配置信息
- **代理状态**: 实时监控各代理工作状态
- **实时数据**: 股价、技术指标、新闻、基本面数据
- **分析报告**: 各代理生成的详细报告
- **交易决策**: 最终的交易建议和参数

### 3. 实时功能
- WebSocket 连接实时更新
- 自动数据刷新
- 状态同步

### 4. LangGraph 智能分析
- **对话式分析**: 通过自然语言进行股票分析
- **智能工作流**: 可视化的分析流程和状态管理
- **工具集成**: 自动调用合适的分析工具
- **内存管理**: 保持对话上下文和分析历史

## 🔌 API 集成

### 后端接口

应用与 TradingAgents 后端通过以下接口通信：

- `POST /api/analysis/start` - 开始分析
- `GET /api/analysis/{id}/status` - 获取分析状态
- `GET /api/analysis/{id}/agents` - 获取代理状态
- `GET /api/analysis/{id}/reports` - 获取分析报告
- `GET /api/analysis/{id}/decision` - 获取交易决策
- `WebSocket /ws/analysis/{id}` - 实时数据推送

### 数据接口

- `GET /api/data/stock/{ticker}` - 股票数据
- `GET /api/data/news/{ticker}` - 新闻数据
- `GET /api/data/technical/{ticker}` - 技术指标
- `GET /api/data/fundamentals/{ticker}` - 基本面数据

## 🎨 界面设计

### 设计原则
- **简洁明了**: 清晰的信息层次
- **响应式**: 适配各种屏幕尺寸
- **可访问性**: 符合 WCAG 标准
- **一致性**: 统一的设计语言

### 主题配置
- 支持深色/浅色主题
- 自定义颜色方案
- 响应式字体大小

## 🔧 开发指南

### 代码规范
- 使用 ESLint 进行代码检查
- 遵循 React/TypeScript 最佳实践
- 组件采用函数式编程

### 组件开发
```tsx
// 示例组件结构
'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';

interface ComponentProps {
  // 定义 props 类型
}

export function Component({ }: ComponentProps) {
  // 组件逻辑
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      {/* 组件内容 */}
    </motion.div>
  );
}
```

### 状态管理
```tsx
// 使用 TanStack Query 管理服务器状态
const { data, isLoading } = useQuery({
  queryKey: ['key'],
  queryFn: fetchData,
});

// 使用 Zustand 管理客户端状态
const useStore = create((set) => ({
  // 状态定义
}));
```

## 📦 构建和部署

### 构建生产版本
```bash
npm run build
npm run start
```

### 类型检查
```bash
npm run type-check
```

### 代码检查
```bash
npm run lint
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源。详见 [LICENSE](LICENSE) 文件。

## ⚠️ 免责声明

本框架仅用于研究目的。交易表现可能因多种因素而异，包括所选的骨干语言模型、模型温度、交易周期、数据质量和其他非确定性因素。**不构成财务、投资或交易建议。**

## 🔗 相关链接

- [TradingAgents 后端项目](https://github.com/TauricResearch/TradingAgents)
- [研究论文](https://arxiv.org/abs/2412.20138)
- [LangGraph 使用指南](./LANGGRAPH_GUIDE.md)
- [LangGraph.js 官方文档](https://langchain-ai.github.io/langgraphjs/)
- [Tauric Research](https://tauric.ai/)
- [Discord 社区](https://discord.com/invite/hk9PGKShPK)

## 📞 支持

如有问题或建议，请通过以下方式联系：

- GitHub Issues
- Discord 社区
- 邮箱: <EMAIL>
